"use client"

import React, { useEffect, useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import Link from "next/link"
import Image from "next/image"
import { usePathname } from "next/navigation"
import { Home as HomeIcon, List as ServicesIcon, Phone as ContactIcon, Clock } from "lucide-react"
import { cn } from "@/lib/utils"

interface NavItem {
  name: string
  url: string
  icon: string
}

// Icon mapping function
const getIconComponent = (iconName: string) => {
  const iconMap = {
    'Home': HomeIcon,
    'List': ServicesIcon,
    'Phone': ContactIcon,
    'Clock': Clock
  };
  return iconMap[iconName as keyof typeof iconMap] || HomeIcon;
};

interface NavBarProps {
  items: NavItem[]
  className?: string
  children?: React.ReactNode
}

export function NavBar({ items, className, children }: NavBarProps) {
  const pathname = usePathname();
  const [scrolled, setScrolled] = useState(false);

  // Simple active state calculation - calculated immediately, no state dependencies
  const isHomePage = pathname === '/' || pathname === '/home';
  const filteredItems = items.filter(item => item.name !== 'Home');
  
  // Find active item
  const activeItem = filteredItems.find(item => {
    if (pathname === item.url) return true;
    return pathname.startsWith(item.url) && item.url !== '/' && item.url.length > 1;
  });

  const activeTab = activeItem ? activeItem.name : '';
  const isLogoActive = isHomePage;

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 10);
    };

    handleScroll(); // Initial check
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <motion.div
      className={cn(
        "fixed top-0 left-0 right-0 z-50 flex justify-center items-start px-4",
        className
      )}
      initial={false}
      animate={{ y: scrolled ? 16 : 24 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
      style={{
        pointerEvents: 'none',
        filter: 'drop-shadow(0 4px 8px rgba(58, 39, 35, 0.2)) drop-shadow(0 1px 3px rgba(176, 124, 112, 0.15))',
        willChange: 'transform',
        position: 'fixed',
        isolation: 'isolate',
        contain: 'layout style'
      }}
    >
      <motion.div
        className={cn(
          "flex items-center justify-center gap-3 sm:gap-4 md:gap-6 py-3 px-4 sm:px-6 md:px-8 rounded-full shadow-lg relative",
          scrolled
            ? "bg-gradient-to-r from-[#3a2723]/85 via-[#6a4840]/85 to-[#3a2723]/85 border border-[#d4b2a7]/25 backdrop-blur-md"
            : "bg-gradient-to-r from-[#3a2723]/70 via-[#6a4840]/70 to-[#3a2723]/70 border border-[#d4b2a7]/20 backdrop-blur-lg"
        )}
        style={{ 
          pointerEvents: 'auto',
          transform: 'translateZ(0)',
          backfaceVisibility: 'hidden'
        }}
        transition={{ duration: 0.3 }}
      >
        {/* Logo as Home Button */}
        <Link
          href="/home"
          className="relative flex items-center cursor-pointer group flex-shrink-0"
        >
          <div className="relative">
            <Image
              src="/glow-by-bry-logo.svg"
              alt="GlowByBry Logo"
              width={120}
              height={48}
              className="object-contain transition-all duration-300 group-hover:scale-105 h-8 sm:h-10 md:h-12 w-auto"
              priority
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
                const fallback = document.createElement('div');
                fallback.innerHTML = 'GlowByBry';
                fallback.className = 'text-[#f8f1ee] font-serif text-lg font-medium';
                target.parentNode?.appendChild(fallback);
              }}
            />
            {/* Logo glow */}
            <div
              className={cn(
                "absolute -inset-1 bg-gradient-to-r from-[#b07c70]/15 via-[#c99a8f]/20 to-[#b07c70]/15 rounded-full blur-sm transition-opacity duration-300",
                isLogoActive ? "opacity-70" : "opacity-30"
              )}
            />
          </div>
        </Link>

        {/* Navigation Items */}
        {filteredItems.map((item) => {
          const Icon = getIconComponent(item.icon);
          const isActive = activeTab === item.name;

          return (
            <Link
              key={item.name}
              href={item.url}
              className={cn(
                "relative cursor-pointer text-sm sm:text-base font-medium px-3 py-2 sm:px-4 md:px-6 rounded-full transition-all duration-200 flex-shrink-0",
                "text-[#f0e6e4]/90 hover:text-[#f8f1ee]",
                isActive && "text-[#f8f1ee]"
              )}
            >
              <span className="hidden sm:inline relative z-10">{item.name}</span>
              <span className="sm:hidden relative z-10 flex items-center justify-center">
                <Icon size={18} strokeWidth={2.5} />
              </span>
              
              {/* Active indicator */}
              <AnimatePresence>
                {isActive && (
                  <motion.div
                    layoutId="active-indicator"
                    className="absolute inset-0 bg-gradient-to-r from-[#b07c70]/50 via-[#c99a8f]/60 to-[#b07c70]/50 rounded-full"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.8 }}
                    transition={{
                      type: "spring",
                      stiffness: 400,
                      damping: 35,
                      duration: 0.3
                    }}
                  >
                    {/* Lamp effect */}
                    <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-gradient-to-r from-[#c99a8f] via-[#d4b2a7] to-[#c99a8f] rounded-t-full">
                      <div className="absolute w-12 h-6 bg-[#d4b2a7]/30 rounded-full blur-md -top-2 left-1/2 transform -translate-x-1/2" />
                      <div className="absolute w-8 h-6 bg-[#c99a8f]/30 rounded-full blur-md -top-1 left-1/2 transform -translate-x-1/2" />
                      <div className="absolute w-4 h-4 bg-[#e8d0c9]/30 rounded-full blur-sm top-0 left-1/2 transform -translate-x-1/2" />
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </Link>
          );
        })}

        {children && (
          <div className="ml-2">
            {children}
          </div>
        )}
      </motion.div>
    </motion.div>
  );
}

export default NavBar