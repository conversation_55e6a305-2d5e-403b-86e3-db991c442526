@tailwind base;
@tailwind components;
@tailwind utilities;

@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
  --glow-primary: #8b5d53;
  --glow-secondary: #b07c70;
  --glow-accent: #d4b2a7;
  --glow-white: #ffffff;
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply outline-none;
  }
  body {
    @apply bg-background text-foreground;
  }
  h1, h2, h3, h4, h5, h6 {
    @apply font-serif font-light;
  }

  /* Special heading styles for large display text */
  .display-heading {
    @apply font-serif font-light tracking-wide;
  }

  /* Playfair Display font class */
  .font-playfair {
    font-family: var(--font-main-serif);
  }

  /* Remove mobile tap highlight */
  input, textarea, select, button {
    -webkit-tap-highlight-color: transparent;
    tap-highlight-color: transparent;
  }

  /* Navbar isolation layer - prevents third-party scripts from affecting navbar positioning */
  [data-navbar-container] {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    width: 100vw !important;
    display: flex !important;
    justify-content: center !important;
    align-items: flex-start !important;
    z-index: 9999 !important;
    isolation: isolate !important;
    contain: layout style size !important;
    backface-visibility: hidden !important;
    transform-style: preserve-3d !important;
    perspective: 1000px !important;
  }

  /* Allow third-party widgets to load naturally */
  .elfsight-app-8a2a1bb6-4be5-4bae-9408-f3a1690fc11a,
  .trustindex-widget {
    width: 100% !important;
    max-width: 100% !important;
  }

  /* Additional protection against layout shifts */
  body {
    overflow-x: hidden !important;
    position: relative !important;
  }

  /* Ensure third-party scripts don't affect document flow */
  body > script,
  head > script {
    display: none !important;
  }

  /* Allow third-party widgets to render properly */
  [class*="elfsight"],
  [class*="trustindex"],
  [id*="elfsight"],
  [id*="trustindex"] {
    max-width: 100% !important;
  }
}

@layer utilities {
  .border {
    border-width: 1px;
    border-style: solid;
  }
  .border-border {
    border-color: var(--border);
  }
}

/* No special CSS needed - using inline styles instead */

/* Spotlight effect animation */
.spotlight-effect {
  position: absolute;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

@keyframes move-spotlight {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(400px, 400px);
  }
}

.group:hover .spotlight-effect {
  opacity: 0.5;
  animation: move-spotlight 4s infinite alternate ease-in-out;
}

/* Enhanced pulse animations */
@keyframes pulse-slow {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.6;
  }
}

@keyframes pulse-slow-reverse {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(-10px) scale(1.02);
  }
}

@keyframes float-delay {
  0%, 100% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(-15px) scale(1.03);
  }
}

.animate-pulse-slow {
  animation: pulse-slow 4s ease-in-out infinite;
}

@keyframes shine {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.animate-pulse-slow-reverse {
  animation: pulse-slow-reverse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-delay {
  animation: float-delay 7s ease-in-out infinite 1s;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) rotate(-5deg);
  }
  100% {
    transform: translateX(100%) rotate(5deg);
  }
}

.animate-shimmer {
  animation: shimmer 2.5s ease-in-out infinite;
}

/* Perspective settings for 3D effects */
.perspective-1000 {
  perspective: 1200px;
  transform-style: preserve-3d;
}

@keyframes gradientX {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes shineEffect {
  0% {
    left: -100%;
    opacity: 0;
  }
  20% {
    opacity: 0.6;
  }
  80% {
    opacity: 0.6;
  }
  100% {
    left: 100%;
    opacity: 0;
  }
}

@keyframes buttonShine {
  0% {
    left: -100%;
    opacity: 0;
  }
  20% {
    left: -50%;
    opacity: 0.5;
  }
  50% {
    left: 0%;
    opacity: 0.8;
  }
  80% {
    left: 50%;
    opacity: 0.5;
  }
  100% {
    left: 100%;
    opacity: 0;
  }
}

.animate-gradient-x {
  animation: gradientX 3s linear infinite;
}

.animate-shine {
  animation: shineEffect 2s ease-in-out;
  animation-iteration-count: infinite;
  opacity: 1;
}

.animate-button-shine {
  animation: buttonShine 2.5s cubic-bezier(0.4, 0, 0.2, 1) infinite;
}

/* Premium button animations */
@keyframes gradientFlow {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes lightPass {
  0% {
    transform: translateX(-100%) skewX(-15deg);
  }
  50% {
    transform: translateX(100%) skewX(-15deg);
  }
  100% {
    transform: translateX(-100%) skewX(-15deg);
  }
}

/* Ripple effect */
.ripple-effect {
  animation: ripple 0.6s linear;
}

@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 0.5;
  }
  60% {
    transform: scale(3);
    opacity: 0.3;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

/* Button hover state animations */
.group:hover .animate-gradient-flow {
  animation: gradientFlow 2s ease infinite;
}

.group:hover .animate-light-pass {
  animation: lightPass 2s ease-in-out infinite;
}

/* Smooth transition defaults */
.transition-all {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Button Animations */
@keyframes gradient-flow {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes light-pass {
  0% {
    transform: translateX(-100%) skewX(-15deg);
  }
  100% {
    transform: translateX(100%) skewX(-15deg);
  }
}

@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 0.5;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

/* Animation Classes */
.animate-gradient-flow {
  animation: gradient-flow 3s ease-in-out infinite;
  background-size: 200% 100%;
}

.animate-light-pass {
  animation: light-pass 1.5s ease-in-out;
}

.animate-ripple {
  animation: ripple 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* Enhanced Hover Effects */
.group:hover .animate-gradient-flow {
  animation-duration: 2s;
}

.group:hover .animate-light-pass {
  animation-iteration-count: infinite;
}

/* Featured Services Grid Hover Effects */
.featured-service-card {
  position: relative;
  overflow: hidden;
}

.featured-service-card .service-overlay {
  position: absolute;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 10;
}

.featured-service-card:hover .service-overlay {
  opacity: 1;
}

.featured-service-card .view-details-btn {
  transform: translateY(20px);
  opacity: 0;
  transition: all 0.3s ease;
  z-index: 20;
}

.featured-service-card:hover .view-details-btn {
  transform: translateY(0);
  opacity: 1;
}

/* Touch device support */
@media (hover: none) {
  .featured-service-card:active .service-overlay {
    opacity: 1;
  }

  .featured-service-card:active .view-details-btn {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Modern Button Animations */
@keyframes subtle-glow {
  0%, 100% {
    opacity: 0.5;
    filter: brightness(1) blur(8px);
  }
  50% {
    opacity: 0.8;
    filter: brightness(1.2) blur(12px);
  }
}

@keyframes gradient-flow {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes pulse-glow {
  0%, 100% {
    transform: scale(1);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

@keyframes light-pass {
  0% {
    transform: translateX(-100%) skewX(-15deg);
    opacity: 0;
  }
  20%, 80% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%) skewX(-15deg);
    opacity: 0;
  }
}

@keyframes sparkle-fade {
  0%, 100% {
    opacity: 0;
    transform: scale(0.5);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 0.4;
  }
  40% {
    transform: scale(0.8);
    opacity: 0.3;
  }
  80% {
    transform: scale(1.5);
    opacity: 0.2;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

/* Animation Utility Classes */
.animate-subtle-glow {
  animation: subtle-glow 3s ease-in-out infinite;
}

.animate-gradient-flow {
  animation: gradient-flow 8s ease infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 4s ease-in-out infinite;
}

.animate-light-pass {
  animation: light-pass 3s ease-out;
}

.animate-sparkle {
  animation: sparkle-fade 2s ease-in-out infinite;
}

.animate-ripple {
  animation-name: ripple;
  animation-duration: 0.7s;
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  animation-fill-mode: forwards;
}

/* Logo Background Effects */
@keyframes float {
  0%, 100% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(-10px) scale(1.02);
  }
}

@keyframes float-delay {
  0%, 100% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(-15px) scale(1.03);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-delay {
  animation: float-delay 7s ease-in-out infinite 1s;
}

/* Enhanced Shimmer Effect */
@keyframes shimmer {
  0% {
    transform: translateX(-100%) rotate(-5deg);
  }
  100% {
    transform: translateX(100%) rotate(5deg);
  }
}

.animate-shimmer {
  animation: shimmer 2.5s ease-in-out infinite;
}

/* Perspective Settings */
.perspective-1000 {
  perspective: 1200px;
  transform-style: preserve-3d;
}

/* Smooth Transition Defaults */
.transition-all {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Star Border Animations */
@keyframes star-movement-top {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes star-movement-bottom {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(-100%);
    opacity: 0;
  }
}

.animate-star-movement-top {
  animation: star-movement-top 6s linear infinite;
}

.animate-star-movement-bottom {
  animation: star-movement-bottom 6s linear infinite;
}

